name: ai_budget_assistant
description: "A comprehensive AI-powered household budgeting application with intelligent spending analysis and personalized financial recommendations."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  
  # HTTP Client
  dio: ^5.4.0
  
  # UI Components
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  
  # Navigation
  go_router: ^12.1.3
  
  # Local Storage
  shared_preferences: ^2.2.2
  path_provider: ^2.1.2
  
  # File Handling
  file_picker: ^6.1.1
  
  # Charts and Visualization
  fl_chart: ^0.66.0
  
  # Animations
  lottie: ^2.7.0
  
  # Date/Time
  intl: ^0.19.0
  
  # Security
  local_auth: ^2.1.7
  crypto: ^3.0.3
  
  # Utils
  equatable: ^2.0.5
  freezed_annotation: ^2.4.1
  json_annotation: ^4.8.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.7
  riverpod_generator: ^2.3.9
  freezed: ^2.4.6
  json_serializable: ^6.7.1
  
  # Linting
  flutter_lints: ^3.0.1
  
  # Testing
  mockito: ^5.4.4
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/animations/
    - assets/icons/
  
  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: fonts/Roboto-Regular.ttf
  #       - asset: fonts/Roboto-Medium.ttf
  #         weight: 500
  #       - asset: fonts/Roboto-Bold.ttf
  #         weight: 700
